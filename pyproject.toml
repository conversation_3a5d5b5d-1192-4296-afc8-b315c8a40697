[project]
name = "hound"
version = "2.0.0"
description = "AI-powered security analysis system with knowledge graph backend"
readme = "README.md"
requires-python = ">=3.10"
dependencies = [
    # LLM Integration
    "openai>=1.0.0",
    "google-generativeai>=0.3.0",
    
    # CLI & Configuration
    "typer>=0.9.0",
    "pydantic>=2.0.0",
    "pydantic-settings>=2.0",
    "pyyaml>=6.0",
    
    # Async Support
    "httpx>=0.25.0",
    
    # Data Processing
    "orjson>=3.9.0",
    
    # Graph algorithms
    "networkx>=3.0",
    "scikit-learn>=1.3.0",  # For clustering
    
    # Development
    "rich>=13.0.0",  # Pretty CLI output
]

[project.optional-dependencies]
dev = [
    "pytest>=7.4.0",
    "pytest-asyncio>=0.21.0",
    "black>=23.0.0",
    "mypy>=1.5.0",
    "ruff>=0.1.0",
]

[project.scripts]
hound = "hound:main"

[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[tool.black]
line-length = 100
target-version = ['py310']

[tool.ruff]
line-length = 100
select = ["E", "F", "I"]

[tool.mypy]
python_version = "3.10"
strict = true

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = "test_*.py"
python_classes = "Test*"
python_functions = "test_*"
addopts = "-v --tb=short"
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as requiring external services",
]